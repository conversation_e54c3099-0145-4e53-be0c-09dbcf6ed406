#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CSDN文章处理模块
基于Selenium的文章获取和处理
"""

import logging
import time
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)

class CsdnArticle:
    """CSDN文章处理类"""

    def __init__(self, driver: webdriver.Chrome):
        """
        初始化CSDN文章处理器

        Args:
            driver: Selenium WebDriver实例
        """
        self.driver = driver
        self.wait = WebDriverWait(driver, 10)
        logger.info("初始化CSDN文章处理器")

    def get_article(self, article_url: str) -> bool:
        """
        获取CSDN文章

        Args:
            article_url: 文章URL

        Returns:
            bool: 成功返回True，失败返回False
        """
        try:
            logger.info(f"正在访问CSDN文章: {article_url}")
            
            # 访问文章页面
            self.driver.get(article_url)
            
            # 等待页面加载
            time.sleep(3)
            
            # 检查页面是否正确加载
            if self._check_article_loaded():
                logger.info("文章页面加载成功")
                return True
            else:
                logger.error("文章页面加载失败")
                return False
                
        except Exception as e:
            logger.error(f"获取文章失败: {e}")
            return False

    def _check_article_loaded(self) -> bool:
        """
        检查文章是否正确加载

        Returns:
            bool: 加载成功返回True
        """
        try:
            # 检查是否存在文章标题
            title_selectors = [
                "h1.title-article",
                ".article-title-box h1",
                "#articleContentId",
                ".blog-content-box"
            ]
            
            for selector in title_selectors:
                try:
                    element = self.wait.until(
                        EC.presence_of_element_located((By.CSS_SELECTOR, selector))
                    )
                    if element:
                        logger.info(f"找到文章内容元素: {selector}")
                        return True
                except TimeoutException:
                    continue
            
            logger.warning("未找到文章内容元素")
            return False
            
        except Exception as e:
            logger.error(f"检查文章加载状态失败: {e}")
            return False

    def get_article_info(self) -> Dict[str, Any]:
        """
        获取文章信息

        Returns:
            Dict: 包含文章信息的字典
        """
        article_info = {
            'title': '',
            'author': '',
            'content': '',
            'publish_time': '',
            'read_count': ''
        }
        
        try:
            # 获取文章标题
            title_selectors = [
                "h1.title-article",
                ".article-title-box h1",
                "h1"
            ]
            
            for selector in title_selectors:
                try:
                    title_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if title_element and title_element.text.strip():
                        article_info['title'] = title_element.text.strip()
                        logger.info(f"获取到文章标题: {article_info['title']}")
                        break
                except NoSuchElementException:
                    continue

            # 获取作者信息
            author_selectors = [
                ".follow-nickName",
                ".username",
                ".author-name"
            ]
            
            for selector in author_selectors:
                try:
                    author_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if author_element and author_element.text.strip():
                        article_info['author'] = author_element.text.strip()
                        logger.info(f"获取到作者: {article_info['author']}")
                        break
                except NoSuchElementException:
                    continue

            # 获取发布时间
            time_selectors = [
                ".time",
                ".publish-time",
                ".article-bar-top .time"
            ]
            
            for selector in time_selectors:
                try:
                    time_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if time_element and time_element.text.strip():
                        article_info['publish_time'] = time_element.text.strip()
                        logger.info(f"获取到发布时间: {article_info['publish_time']}")
                        break
                except NoSuchElementException:
                    continue

            # 获取阅读量
            read_selectors = [
                ".read-count",
                ".view-count",
                ".article-bar-top .read-count"
            ]
            
            for selector in read_selectors:
                try:
                    read_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if read_element and read_element.text.strip():
                        article_info['read_count'] = read_element.text.strip()
                        logger.info(f"获取到阅读量: {article_info['read_count']}")
                        break
                except NoSuchElementException:
                    continue

            # 获取文章内容
            content_selectors = [
                "#content_views",
                ".article-content",
                ".blog-content-box"
            ]
            
            for selector in content_selectors:
                try:
                    content_element = self.driver.find_element(By.CSS_SELECTOR, selector)
                    if content_element:
                        article_info['content'] = content_element.text.strip()
                        logger.info(f"获取到文章内容，长度: {len(article_info['content'])}")
                        break
                except NoSuchElementException:
                    continue

            return article_info
            
        except Exception as e:
            logger.error(f"获取文章信息失败: {e}")
            return article_info

    def scroll_to_bottom(self):
        """滚动到页面底部"""
        try:
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            logger.info("已滚动到页面底部")
        except Exception as e:
            logger.error(f"滚动页面失败: {e}")

    def take_screenshot(self, file_path: str) -> bool:
        """
        截取页面截图

        Args:
            file_path: 截图保存路径

        Returns:
            bool: 成功返回True
        """
        try:
            self.driver.save_screenshot(file_path)
            logger.info(f"截图已保存: {file_path}")
            return True
        except Exception as e:
            logger.error(f"截图失败: {e}")
            return False

    def close(self):
        """关闭浏览器"""
        try:
            if self.driver:
                self.driver.quit()
                logger.info("浏览器已关闭")
        except Exception as e:
            logger.error(f"关闭浏览器失败: {e}")
